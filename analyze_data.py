import pandas as pd
import numpy as np
from datetime import datetime
import re

def analyze_excel_data():
    try:
        # 读取Excel文件
        df = pd.read_excel('作业登记表ABC栋+捷通202507.xlsx', sheet_name=0)
        
        print("=== 数据基本信息 ===")
        print(f"数据形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
        
        print("\n=== 日期格式分析 ===")
        # 分析日期列
        unique_dates = df['日期'].unique()
        print(f"唯一日期数量: {len(unique_dates)}")
        print(f"日期样例: {unique_dates[:10]}")
        
        # 尝试解析日期格式
        date_patterns = []
        for date_str in unique_dates[:20]:  # 检查前20个日期
            if pd.notna(date_str):
                date_patterns.append(str(date_str))
        
        print(f"日期格式样例: {date_patterns}")
        
        print("\n=== 栋别分析 ===")
        building_counts = df['栋别'].value_counts()
        print("栋别分布:")
        print(building_counts)
        
        print("\n=== 作业类型分析 ===")
        work_type_counts = df['作业类型'].value_counts()
        print("作业类型分布:")
        print(work_type_counts)
        
        print("\n=== 时间格式分析 ===")
        print("起始时间样例:", df['起始时间'].dropna().head(10).tolist())
        print("截止时间样例:", df['截止时间'].dropna().head(10).tolist())
        print("作业时长样例:", df['作业时长'].dropna().head(10).tolist())
        
        print("\n=== 数量分析 ===")
        # 尝试转换数量为数值
        df_copy = df.copy()
        df_copy['数量_数值'] = pd.to_numeric(df_copy['数量'], errors='coerce')
        quantity_stats = df_copy['数量_数值'].describe()
        print("数量统计:")
        print(quantity_stats)
        
        print("\n=== 方向分析 ===")
        direction_counts = df['方向'].value_counts()
        print("方向分布:")
        print(direction_counts)
        
        print("\n=== 作业内容分析 ===")
        content_counts = df['作业内容'].value_counts()
        print("作业内容分布 (前10):")
        print(content_counts.head(10))
        
        print("\n=== 缺失值分析 ===")
        missing_values = df.isnull().sum()
        print("各列缺失值数量:")
        print(missing_values)
        
        # 保存分析结果到CSV以便进一步查看
        df.to_csv('作业登记表_分析.csv', index=False, encoding='utf-8-sig')
        print("\n数据已保存到 '作业登记表_分析.csv'")
        
        return df
        
    except Exception as e:
        print(f'分析数据时出错: {e}')
        return None

if __name__ == "__main__":
    df = analyze_excel_data()
