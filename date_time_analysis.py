import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import re

def parse_date_time():
    try:
        # 读取Excel文件
        df = pd.read_excel('作业登记表ABC栋+捷通202507.xlsx', sheet_name=0)
        
        print("=== 日期时间解析分析 ===")
        
        # 处理日期格式 (4/21 表示4月21日)
        def parse_date(date_str):
            if pd.isna(date_str):
                return None
            try:
                # 假设是2025年的数据
                date_str = str(date_str).strip()
                if '/' in date_str:
                    month, day = date_str.split('/')
                    return datetime(2025, int(month), int(day))
                return None
            except:
                return None
        
        # 处理时间格式 (08:30)
        def parse_time(time_str):
            if pd.isna(time_str):
                return None
            try:
                time_str = str(time_str).strip()
                if ':' in time_str:
                    hour, minute = time_str.split(':')
                    return timedelta(hours=int(hour), minutes=int(minute))
                return None
            except:
                return None
        
        # 处理时长格式 (1:00)
        def parse_duration(duration_str):
            if pd.isna(duration_str):
                return None
            try:
                duration_str = str(duration_str).strip()
                if ':' in duration_str:
                    hour, minute = duration_str.split(':')
                    return timedelta(hours=int(hour), minutes=int(minute))
                return None
            except:
                return None
        
        # 应用解析函数
        df['日期_解析'] = df['日期'].apply(parse_date)
        df['起始时间_解析'] = df['起始时间'].apply(parse_time)
        df['截止时间_解析'] = df['截止时间'].apply(parse_time)
        df['作业时长_解析'] = df['作业时长'].apply(parse_duration)
        
        # 创建完整的开始和结束时间戳
        df['开始时间戳'] = df.apply(lambda row: 
            row['日期_解析'] + row['起始时间_解析'] 
            if pd.notna(row['日期_解析']) and pd.notna(row['起始时间_解析']) 
            else None, axis=1)
        
        df['结束时间戳'] = df.apply(lambda row: 
            row['日期_解析'] + row['截止时间_解析'] 
            if pd.notna(row['日期_解析']) and pd.notna(row['截止时间_解析']) 
            else None, axis=1)
        
        # 计算实际时长并与记录的时长比较
        df['计算时长'] = df.apply(lambda row:
            row['结束时间戳'] - row['开始时间戳']
            if pd.notna(row['开始时间戳']) and pd.notna(row['结束时间戳'])
            else None, axis=1)
        
        print("解析结果样例:")
        sample_cols = ['日期', '日期_解析', '起始时间', '起始时间_解析', '作业时长', '作业时长_解析', '计算时长']
        print(df[sample_cols].head(10))
        
        print("\n=== 日期范围分析 ===")
        valid_dates = df['日期_解析'].dropna()
        if len(valid_dates) > 0:
            print(f"最早日期: {valid_dates.min()}")
            print(f"最晚日期: {valid_dates.max()}")
            print(f"日期跨度: {(valid_dates.max() - valid_dates.min()).days} 天")
        
        print("\n=== 时长一致性检查 ===")
        # 检查记录的时长与计算的时长是否一致
        df['时长差异'] = df.apply(lambda row:
            abs((row['计算时长'] - row['作业时长_解析']).total_seconds()) 
            if pd.notna(row['计算时长']) and pd.notna(row['作业时长_解析'])
            else None, axis=1)
        
        inconsistent = df[df['时长差异'] > 0]
        print(f"时长不一致的记录数: {len(inconsistent)}")
        if len(inconsistent) > 0:
            print("不一致的样例:")
            print(inconsistent[['日期', '起始时间', '截止时间', '作业时长', '计算时长']].head())
        
        print("\n=== 按日期统计作业量 ===")
        daily_stats = df.groupby('日期_解析').agg({
            '数量': lambda x: pd.to_numeric(x, errors='coerce').sum(),
            '作业时长_解析': lambda x: sum([t.total_seconds()/3600 for t in x if pd.notna(t)]),
            '栋别': 'count'
        }).round(2)
        daily_stats.columns = ['总数量', '总作业时长(小时)', '作业次数']
        print(daily_stats.head(10))
        
        print("\n=== 按栋别统计 ===")
        building_stats = df.groupby('栋别').agg({
            '数量': lambda x: pd.to_numeric(x, errors='coerce').sum(),
            '作业时长_解析': lambda x: sum([t.total_seconds()/3600 for t in x if pd.notna(t)]),
            '日期': 'count'
        }).round(2)
        building_stats.columns = ['总数量', '总作业时长(小时)', '作业次数']
        print(building_stats)
        
        # 保存处理后的数据
        df.to_csv('作业登记表_时间解析.csv', index=False, encoding='utf-8-sig')
        print("\n处理后的数据已保存到 '作业登记表_时间解析.csv'")
        
        return df
        
    except Exception as e:
        print(f'解析日期时间时出错: {e}')
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    df = parse_date_time()
