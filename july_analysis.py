import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json

def analyze_july_data():
    try:
        # 读取Excel文件
        df = pd.read_excel('作业登记表ABC栋+捷通202507.xlsx', sheet_name=0)
        
        # 处理日期格式
        def parse_date(date_str):
            if pd.isna(date_str):
                return None
            try:
                date_str = str(date_str).strip()
                if '/' in date_str:
                    month, day = date_str.split('/')
                    return datetime(2025, int(month), int(day))
                return None
            except:
                return None
        
        # 处理时间格式
        def parse_time(time_str):
            if pd.isna(time_str):
                return None
            try:
                time_str = str(time_str).strip()
                if ':' in time_str:
                    hour, minute = time_str.split(':')
                    return timedelta(hours=int(hour), minutes=int(minute))
                return None
            except:
                return None
        
        # 应用解析函数
        df['日期_解析'] = df['日期'].apply(parse_date)
        df['起始时间_解析'] = df['起始时间'].apply(parse_time)
        df['截止时间_解析'] = df['截止时间'].apply(parse_time)
        df['作业时长_解析'] = df['作业时长'].apply(lambda x: parse_time(x) if pd.notna(x) else None)
        
        # 筛选7月份数据
        july_data = df[df['日期_解析'].dt.month == 7].copy()
        
        print("=== 7月份数据分析报告 ===")
        print(f"7月份总作业数: {len(july_data)}")
        
        if len(july_data) == 0:
            print("没有找到7月份的数据")
            return None
        
        # 转换数量为数值
        july_data['数量_数值'] = pd.to_numeric(july_data['数量'], errors='coerce')
        
        # 基本统计
        print(f"数据时间范围: {july_data['日期_解析'].min()} 到 {july_data['日期_解析'].max()}")
        print(f"记录天数: {july_data['日期_解析'].nunique()}")
        
        # 按日期统计
        daily_stats = july_data.groupby('日期_解析').agg({
            '数量_数值': lambda x: x.sum(),
            '作业时长_解析': lambda x: sum([t.total_seconds()/3600 for t in x if pd.notna(t)]),
            '栋别': 'count'
        }).round(2)
        daily_stats.columns = ['总数量', '总作业时长(小时)', '作业次数']
        daily_stats['日期'] = daily_stats.index.strftime('%m/%d')
        
        print("\n=== 每日作业统计 ===")
        print(daily_stats)
        
        # 按栋别统计
        building_stats = july_data.groupby('栋别').agg({
            '数量_数值': lambda x: x.sum(),
            '作业时长_解析': lambda x: sum([t.total_seconds()/3600 for t in x if pd.notna(t)]),
            '日期': 'count'
        }).round(2)
        building_stats.columns = ['总数量', '总作业时长(小时)', '作业次数']
        
        print("\n=== 栋别作业统计 ===")
        print(building_stats)
        
        # 作业类型统计
        work_type_stats = july_data['作业类型'].value_counts()
        print("\n=== 作业类型统计 ===")
        print(work_type_stats)
        
        # 作业内容统计
        work_content_stats = july_data['作业内容'].value_counts().head(10)
        print("\n=== 作业内容统计(前10) ===")
        print(work_content_stats)
        
        # 方向统计
        direction_stats = july_data['方向'].value_counts().head(10)
        print("\n=== 方向统计(前10) ===")
        print(direction_stats)
        
        # 生成图表数据
        chart_data = {
            'daily_work_counts': {
                'dates': daily_stats['日期'].tolist(),
                'counts': daily_stats['作业次数'].tolist(),
                'avg': daily_stats['作业次数'].mean()
            },
            'building_distribution': {
                'buildings': building_stats.index.tolist(),
                'counts': building_stats['作业次数'].tolist()
            },
            'work_type_distribution': {
                'types': work_type_stats.index.tolist(),
                'counts': work_type_stats.values.tolist()
            },
            'work_content_distribution': {
                'contents': work_content_stats.index.tolist(),
                'counts': work_content_stats.values.tolist()
            },
            'direction_distribution': {
                'directions': direction_stats.index.tolist(),
                'counts': direction_stats.values.tolist()
            },
            'calendar_data': [[index.strftime('%Y-%m-%d'), row['作业次数']]
                             for index, row in daily_stats.iterrows()],
            'summary_stats': {
                'total_works': len(july_data),
                'total_days': july_data['日期_解析'].nunique(),
                'avg_daily_works': round(len(july_data) / july_data['日期_解析'].nunique(), 2),
                'std_daily_works': round(daily_stats['作业次数'].std(), 2),
                'total_quantity': july_data['数量_数值'].sum(),
                'total_hours': sum([t.total_seconds()/3600 for t in july_data['作业时长_解析'] if pd.notna(t)])
            }
        }
        
        # 保存图表数据到JSON文件
        with open('july_chart_data.json', 'w', encoding='utf-8') as f:
            json.dump(chart_data, f, ensure_ascii=False, indent=2)
        
        print("\n=== 关键指标 ===")
        print(f"总作业数: {chart_data['summary_stats']['total_works']}")
        print(f"记录天数: {chart_data['summary_stats']['total_days']}")
        print(f"日均作业数: {chart_data['summary_stats']['avg_daily_works']}")
        print(f"日作业数标准差: {chart_data['summary_stats']['std_daily_works']}")
        print(f"总数量: {chart_data['summary_stats']['total_quantity']}")
        print(f"总作业时长: {chart_data['summary_stats']['total_hours']:.1f}小时")
        
        # 保存7月份数据
        july_data.to_csv('july_data_analysis.csv', index=False, encoding='utf-8-sig')
        print("\n7月份数据已保存到 'july_data_analysis.csv'")
        
        return chart_data
        
    except Exception as e:
        print(f'分析7月份数据时出错: {e}')
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    chart_data = analyze_july_data()
