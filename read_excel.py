import pandas as pd
import sys

def read_excel_file():
    try:
        # 读取Excel文件
        excel_file = pd.ExcelFile('作业登记表ABC栋+捷通202507.xlsx')
        print('工作表名称:', excel_file.sheet_names)
        
        # 读取第一个工作表
        df = pd.read_excel('作业登记表ABC栋+捷通202507.xlsx', sheet_name=0)
        print('\n数据形状:', df.shape)
        print('\n列名:')
        for i, col in enumerate(df.columns):
            print(f'{i}: {col}')
        
        print('\n前10行数据:')
        print(df.head(10))
        
        print('\n数据类型:')
        print(df.dtypes)
        
        # 检查日期列的格式
        print('\n检查可能的日期列:')
        for col in df.columns:
            if df[col].dtype == 'object':  # 字符串类型
                sample_values = df[col].dropna().head(10).tolist()
                print(f'{col}: {sample_values}')
        
        return df
        
    except Exception as e:
        print(f'读取Excel文件时出错: {e}')
        return None

if __name__ == "__main__":
    df = read_excel_file()
